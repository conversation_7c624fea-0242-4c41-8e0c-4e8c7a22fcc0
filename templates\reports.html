{% extends "base.html" %}

{% block title %}التقارير - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title">
        <i class="fas fa-chart-bar me-3"></i>
        التقارير والإحصائيات
    </h1>
    <button class="btn btn-primary" onclick="window.print()">
        <i class="fas fa-print me-2"></i>
        طباعة التقرير
    </button>
</div>

<!-- Report Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i>
            فلاتر التقرير
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="reportType" class="form-label">نوع التقرير</label>
                <select class="form-select" id="reportType">
                    <option value="sales">تقرير المبيعات</option>
                    <option value="inventory">تقرير المخزون</option>
                    <option value="financial">التقرير المالي</option>
                    <option value="expiry">تقرير انتهاء الصلاحية</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="dateFrom" value="2024-01-01">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="dateTo" value="2024-01-15">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-primary w-100">
                    <i class="fas fa-chart-line me-2"></i>
                    إنشاء التقرير
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Sales Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card-success">
            <div class="card-body text-center">
                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                <h4>إجمالي المبيعات</h4>
                <h2>1,250</h2>
                <small>عملية بيع</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card-info">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                <h4>إجمالي الإيرادات</h4>
                <h2>125,750.50</h2>
                <small>ر.س</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card-warning">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <h4>متوسط البيع</h4>
                <h2>100.60</h2>
                <small>ر.س</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-pills fa-2x mb-2"></i>
                <h4>الأصناف المباعة</h4>
                <h2>3,450</h2>
                <small>قطعة</small>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    مبيعات الأسبوعين الماضيين
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" height="100"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع المبيعات حسب الفئة
                </h5>
            </div>
            <div class="card-body">
                <canvas id="categoryChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Top Selling Medicines -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    أكثر الأدوية مبيعاً
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الترتيب</th>
                                <th>الدواء</th>
                                <th>الكمية المباعة</th>
                                <th>الإيرادات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="badge bg-warning">1</span></td>
                                <td>باراسيتامول</td>
                                <td>450</td>
                                <td>6,975.00 ر.س</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-secondary">2</span></td>
                                <td>أموكسيسيلين</td>
                                <td>320</td>
                                <td>14,400.00 ر.س</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-secondary">3</span></td>
                                <td>فيتامين د</td>
                                <td>280</td>
                                <td>7,210.00 ر.س</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-secondary">4</span></td>
                                <td>أسبرين</td>
                                <td>250</td>
                                <td>3,000.00 ر.س</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-secondary">5</span></td>
                                <td>أوميجا 3</td>
                                <td>180</td>
                                <td>15,300.00 ر.س</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تنبيهات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning mb-3">
                    <h6><i class="fas fa-box me-2"></i>مخزون منخفض</h6>
                    <p class="mb-0">12 صنف يحتاج إلى إعادة تموين</p>
                </div>
                
                <div class="alert alert-danger mb-3">
                    <h6><i class="fas fa-calendar-times me-2"></i>انتهاء صلاحية</h6>
                    <p class="mb-0">8 أصناف تنتهي صلاحيتها خلال 30 يوم</p>
                </div>
                
                <div class="alert alert-info mb-0">
                    <h6><i class="fas fa-chart-line me-2"></i>نمو المبيعات</h6>
                    <p class="mb-0">زيادة 15% في المبيعات مقارنة بالشهر الماضي</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Sales Report -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>
            تقرير المبيعات التفصيلي
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>عدد العمليات</th>
                        <th>إجمالي الإيرادات</th>
                        <th>متوسط قيمة البيع</th>
                        <th>الأصناف المباعة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2024-01-15</td>
                        <td>25</td>
                        <td>2,450.75 ر.س</td>
                        <td>98.03 ر.س</td>
                        <td>67</td>
                    </tr>
                    <tr>
                        <td>2024-01-14</td>
                        <td>32</td>
                        <td>3,120.50 ر.س</td>
                        <td>97.52 ر.س</td>
                        <td>89</td>
                    </tr>
                    <tr>
                        <td>2024-01-13</td>
                        <td>28</td>
                        <td>2,890.25 ر.س</td>
                        <td>103.22 ر.س</td>
                        <td>76</td>
                    </tr>
                    <tr>
                        <td>2024-01-12</td>
                        <td>35</td>
                        <td>3,567.00 ر.س</td>
                        <td>101.91 ر.س</td>
                        <td>98</td>
                    </tr>
                    <tr>
                        <td>2024-01-11</td>
                        <td>22</td>
                        <td>2,156.75 ر.s</td>
                        <td>98.03 ر.س</td>
                        <td>58</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Sales Chart
const salesCtx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(salesCtx, {
    type: 'line',
    data: {
        labels: ['11 يناير', '12 يناير', '13 يناير', '14 يناير', '15 يناير'],
        datasets: [{
            label: 'المبيعات اليومية (ر.س)',
            data: [2156.75, 3567.00, 2890.25, 3120.50, 2450.75],
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Category Chart
const categoryCtx = document.getElementById('categoryChart').getContext('2d');
const categoryChart = new Chart(categoryCtx, {
    type: 'doughnut',
    data: {
        labels: ['مسكنات', 'مضادات حيوية', 'فيتامينات', 'مكملات غذائية'],
        datasets: [{
            data: [35, 25, 20, 20],
            backgroundColor: [
                'rgba(102, 126, 234, 0.8)',
                'rgba(118, 75, 162, 0.8)',
                'rgba(86, 171, 47, 0.8)',
                'rgba(255, 107, 107, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
