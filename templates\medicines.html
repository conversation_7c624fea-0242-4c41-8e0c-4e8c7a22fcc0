{% extends "base.html" %}

{% block title %}إدارة الأدوية - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title">
        <i class="fas fa-pills me-3"></i>
        إدارة الأدوية
    </h1>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMedicineModal">
        <i class="fas fa-plus me-2"></i>
        إضافة دواء جديد
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" placeholder="البحث عن دواء...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select">
                    <option value="">جميع الفئات</option>
                    <option value="مسكنات">مسكنات</option>
                    <option value="مضادات حيوية">مضادات حيوية</option>
                    <option value="فيتامينات">فيتامينات</option>
                    <option value="مكملات غذائية">مكملات غذائية</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select">
                    <option value="">حالة المخزون</option>
                    <option value="متوفر">متوفر</option>
                    <option value="منخفض">منخفض</option>
                    <option value="نفد">نفد</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-primary w-100">
                    <i class="fas fa-filter me-2"></i>
                    تصفية
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Medicines Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الأدوية
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم الدواء</th>
                        <th>الفئة</th>
                        <th>السعر</th>
                        <th>المخزون</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for medicine in medicines %}
                    <tr>
                        <td>{{ medicine.id }}</td>
                        <td>
                            <strong>{{ medicine.name }}</strong>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ medicine.category }}</span>
                        </td>
                        <td>{{ "%.2f"|format(medicine.price) }} ر.س</td>
                        <td>
                            <span class="badge {% if medicine.stock > 50 %}bg-success{% elif medicine.stock > 20 %}bg-warning{% else %}bg-danger{% endif %}">
                                {{ medicine.stock }}
                            </span>
                        </td>
                        <td>{{ medicine.expiry }}</td>
                        <td>
                            {% if medicine.stock > 50 %}
                                <span class="badge bg-success">متوفر</span>
                            {% elif medicine.stock > 0 %}
                                <span class="badge bg-warning">منخفض</span>
                            {% else %}
                                <span class="badge bg-danger">نفد</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1">السابق</a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item">
                    <a class="page-link" href="#">التالي</a>
                </li>
            </ul>
        </nav>
    </div>
</div>

<!-- Add Medicine Modal -->
<div class="modal fade" id="addMedicineModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة دواء جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="medicineName" class="form-label">اسم الدواء</label>
                            <input type="text" class="form-control" id="medicineName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="medicineCategory" class="form-label">الفئة</label>
                            <select class="form-select" id="medicineCategory" required>
                                <option value="">اختر الفئة</option>
                                <option value="مسكنات">مسكنات</option>
                                <option value="مضادات حيوية">مضادات حيوية</option>
                                <option value="فيتامينات">فيتامينات</option>
                                <option value="مكملات غذائية">مكملات غذائية</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="medicinePrice" class="form-label">السعر (ر.س)</label>
                            <input type="number" class="form-control" id="medicinePrice" step="0.01" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="medicineStock" class="form-label">الكمية</label>
                            <input type="number" class="form-control" id="medicineStock" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="medicineExpiry" class="form-label">تاريخ الانتهاء</label>
                            <input type="date" class="form-control" id="medicineExpiry" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="medicineManufacturer" class="form-label">الشركة المصنعة</label>
                            <input type="text" class="form-control" id="medicineManufacturer">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="medicineBarcode" class="form-label">الباركود</label>
                            <input type="text" class="form-control" id="medicineBarcode">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="medicineDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="medicineDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>
                    حفظ الدواء
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
