from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import os

app = Flask(__name__)
app.secret_key = 'pharmacy_management_secret_key_2024'

# الصفحة الرئيسية - Dashboard
@app.route('/')
def index():
    # بيانات وهمية للعرض
    stats = {
        'total_medicines': 150,
        'low_stock_items': 12,
        'today_sales': 25,
        'total_revenue': 15750.50
    }
    return render_template('index.html', stats=stats)

# صفحة إدارة الأدوية
@app.route('/medicines')
def medicines():
    # بيانات وهمية للأدوية
    medicines_list = [
        {'id': 1, 'name': 'باراسيتامول', 'category': 'مسكنات', 'price': 15.50, 'stock': 100, 'expiry': '2025-12-31'},
        {'id': 2, 'name': 'أموكسيسيلين', 'category': 'مضادات حيوية', 'price': 45.00, 'stock': 50, 'expiry': '2025-06-15'},
        {'id': 3, 'name': 'فيتامين د', 'category': 'فيتامينات', 'price': 25.75, 'stock': 75, 'expiry': '2026-03-20'},
        {'id': 4, 'name': 'أسبرين', 'category': 'مسكنات', 'price': 12.00, 'stock': 200, 'expiry': '2025-09-10'},
        {'id': 5, 'name': 'أوميجا 3', 'category': 'مكملات غذائية', 'price': 85.00, 'stock': 30, 'expiry': '2025-11-05'}
    ]
    return render_template('medicines.html', medicines=medicines_list)

# صفحة إدارة المبيعات
@app.route('/sales')
def sales():
    # بيانات وهمية للمبيعات
    sales_list = [
        {'id': 1, 'date': '2024-01-15', 'customer': 'أحمد محمد', 'total': 125.50, 'items': 3},
        {'id': 2, 'date': '2024-01-15', 'customer': 'فاطمة علي', 'total': 67.25, 'items': 2},
        {'id': 3, 'date': '2024-01-14', 'customer': 'محمد حسن', 'total': 89.75, 'items': 4},
        {'id': 4, 'date': '2024-01-14', 'customer': 'سارة أحمد', 'total': 156.00, 'items': 5}
    ]
    return render_template('sales.html', sales=sales_list)

# صفحة إدارة المخزون
@app.route('/inventory')
def inventory():
    # بيانات وهمية للمخزون
    inventory_list = [
        {'medicine': 'باراسيتامول', 'current_stock': 100, 'min_stock': 20, 'status': 'متوفر'},
        {'medicine': 'أموكسيسيلين', 'current_stock': 50, 'min_stock': 30, 'status': 'متوفر'},
        {'medicine': 'فيتامين د', 'current_stock': 15, 'min_stock': 25, 'status': 'منخفض'},
        {'medicine': 'أسبرين', 'current_stock': 200, 'min_stock': 50, 'status': 'متوفر'},
        {'medicine': 'أوميجا 3', 'current_stock': 5, 'min_stock': 15, 'status': 'نفد'}
    ]
    return render_template('inventory.html', inventory=inventory_list)

# صفحة التقارير
@app.route('/reports')
def reports():
    return render_template('reports.html')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
