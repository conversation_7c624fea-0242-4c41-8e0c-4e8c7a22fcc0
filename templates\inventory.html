{% extends "base.html" %}

{% block title %}إدارة المخزون - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title">
        <i class="fas fa-boxes me-3"></i>
        إدارة المخزون
    </h1>
    <div>
        <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#addStockModal">
            <i class="fas fa-plus me-2"></i>
            إضافة مخزون
        </button>
        <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#stockAlertModal">
            <i class="fas fa-exclamation-triangle me-2"></i>
            تنبيهات المخزون
        </button>
    </div>
</div>

<!-- Inventory Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card-success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h4>متوفر</h4>
                <h2>120</h2>
                <small>صنف</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card-warning">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h4>مخزون منخفض</h4>
                <h2>12</h2>
                <small>صنف</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card-danger">
            <div class="card-body text-center">
                <i class="fas fa-times-circle fa-2x mb-2"></i>
                <h4>نفد المخزون</h4>
                <h2>3</h2>
                <small>صنف</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card-info">
            <div class="card-body text-center">
                <i class="fas fa-calendar-times fa-2x mb-2"></i>
                <h4>منتهي الصلاحية</h4>
                <h2>8</h2>
                <small>صنف</small>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" placeholder="البحث عن دواء...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="متوفر">متوفر</option>
                    <option value="منخفض">مخزون منخفض</option>
                    <option value="نفد">نفد المخزون</option>
                    <option value="منتهي">منتهي الصلاحية</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select">
                    <option value="">جميع الفئات</option>
                    <option value="مسكنات">مسكنات</option>
                    <option value="مضادات حيوية">مضادات حيوية</option>
                    <option value="فيتامينات">فيتامينات</option>
                    <option value="مكملات غذائية">مكملات غذائية</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-primary w-100">
                    <i class="fas fa-filter me-2"></i>
                    تصفية
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Inventory Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            حالة المخزون
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الدواء</th>
                        <th>المخزون الحالي</th>
                        <th>الحد الأدنى</th>
                        <th>الحالة</th>
                        <th>آخر تحديث</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in inventory %}
                    <tr>
                        <td>
                            <strong>{{ item.medicine }}</strong>
                        </td>
                        <td>
                            <span class="badge {% if item.current_stock > item.min_stock %}bg-success{% elif item.current_stock > 0 %}bg-warning{% else %}bg-danger{% endif %} fs-6">
                                {{ item.current_stock }}
                            </span>
                        </td>
                        <td>{{ item.min_stock }}</td>
                        <td>
                            {% if item.status == 'متوفر' %}
                                <span class="badge bg-success">{{ item.status }}</span>
                            {% elif item.status == 'منخفض' %}
                                <span class="badge bg-warning">{{ item.status }}</span>
                            {% else %}
                                <span class="badge bg-danger">{{ item.status }}</span>
                            {% endif %}
                        </td>
                        <td>2024-01-15</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-success" title="إضافة مخزون" data-bs-toggle="modal" data-bs-target="#addStockModal">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-primary" title="تعديل الحد الأدنى">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info" title="سجل الحركات">
                                    <i class="fas fa-history"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Stock Modal -->
<div class="modal fade" id="addStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مخزون
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="stockMedicine" class="form-label">الدواء</label>
                        <select class="form-select" id="stockMedicine" required>
                            <option value="">اختر الدواء</option>
                            <option value="1">باراسيتامول</option>
                            <option value="2">أموكسيسيلين</option>
                            <option value="3">فيتامين د</option>
                            <option value="4">أسبرين</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="stockQuantity" class="form-label">الكمية المضافة</label>
                        <input type="number" class="form-control" id="stockQuantity" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="stockBatch" class="form-label">رقم الدفعة</label>
                        <input type="text" class="form-control" id="stockBatch">
                    </div>
                    <div class="mb-3">
                        <label for="stockExpiry" class="form-label">تاريخ الانتهاء</label>
                        <input type="date" class="form-control" id="stockExpiry" required>
                    </div>
                    <div class="mb-3">
                        <label for="stockSupplier" class="form-label">المورد</label>
                        <input type="text" class="form-control" id="stockSupplier">
                    </div>
                    <div class="mb-3">
                        <label for="stockNotes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="stockNotes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success">
                    <i class="fas fa-save me-2"></i>
                    إضافة المخزون
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Stock Alert Modal -->
<div class="modal fade" id="stockAlertModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تنبيهات المخزون
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>مخزون منخفض</h6>
                    <ul class="mb-0">
                        <li>فيتامين د - المخزون الحالي: 15 (الحد الأدنى: 25)</li>
                        <li>أوميجا 3 - المخزون الحالي: 8 (الحد الأدنى: 15)</li>
                    </ul>
                </div>
                
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times-circle me-2"></i>نفد المخزون</h6>
                    <ul class="mb-0">
                        <li>كالسيوم - المخزون الحالي: 0</li>
                        <li>مغنيسيوم - المخزون الحالي: 0</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-calendar-times me-2"></i>انتهاء صلاحية قريب (خلال 30 يوم)</h6>
                    <ul class="mb-0">
                        <li>أموكسيسيلين - ينتهي في: 2024-02-10</li>
                        <li>أسبرين - ينتهي في: 2024-02-15</li>
                        <li>فيتامين ب12 - ينتهي في: 2024-02-20</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">فهمت</button>
            </div>
        </div>
    </div>
</div>

<style>
.stat-card-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
}
</style>
{% endblock %}
